# Variables for JWT Token Manager and Authorizer
# Note: region and environment variables are defined in variable.tf

variable "jwt_secret_key" {
  description = "Secret key for JWT token signing (should be at least 32 characters)"
  type        = string
  sensitive   = true

  validation {
    condition     = length(var.jwt_secret_key) >= 32
    error_message = "JWT secret key must be at least 32 characters long."
  }
}

variable "access_token_ttl" {
  description = "Access token TTL in seconds"
  type        = string
  default     = "900" # 15 minutes

  validation {
    condition     = can(tonumber(var.access_token_ttl)) && tonumber(var.access_token_ttl) > 0
    error_message = "Access token TTL must be a positive number."
  }
}

variable "refresh_token_ttl" {
  description = "Refresh token TTL in seconds"
  type        = string
  default     = "604800" # 7 days

  validation {
    condition     = can(tonumber(var.refresh_token_ttl)) && tonumber(var.refresh_token_ttl) > 0
    error_message = "Refresh token TTL must be a positive number."
  }
}

variable "token_cache_ttl" {
  description = "Token cache TTL in seconds"
  type        = string
  default     = "300" # 5 minutes

  validation {
    condition     = can(tonumber(var.token_cache_ttl)) && tonumber(var.token_cache_ttl) > 0
    error_message = "Token cache TTL must be a positive number."
  }
}

variable "lambda_memory_size" {
  description = "Memory size for Lambda function in MB"
  type        = number
  default     = 512

  validation {
    condition     = var.lambda_memory_size >= 128 && var.lambda_memory_size <= 10240
    error_message = "Lambda memory size must be between 128 and 10240 MB."
  }
}

variable "lambda_timeout" {
  description = "Timeout for Lambda function in seconds"
  type        = number
  default     = 30

  validation {
    condition     = var.lambda_timeout >= 1 && var.lambda_timeout <= 900
    error_message = "Lambda timeout must be between 1 and 900 seconds."
  }
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 14

  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.log_retention_days)
    error_message = "Log retention days must be a valid CloudWatch retention period."
  }
}

variable "enable_api_gateway_logging" {
  description = "Enable API Gateway access logging"
  type        = bool
  default     = true
}

variable "api_gateway_throttle_rate_limit" {
  description = "API Gateway throttle rate limit (requests per second)"
  type        = number
  default     = 1000

  validation {
    condition     = var.api_gateway_throttle_rate_limit > 0
    error_message = "API Gateway throttle rate limit must be positive."
  }
}

variable "api_gateway_throttle_burst_limit" {
  description = "API Gateway throttle burst limit"
  type        = number
  default     = 2000

  validation {
    condition     = var.api_gateway_throttle_burst_limit > 0
    error_message = "API Gateway throttle burst limit must be positive."
  }
}

variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# Local values for computed tags
locals {
  common_tags = merge(
    {
      Project     = "YNP01"
      Environment = var.environment
      ManagedBy   = "Terraform"
      Component   = "JWT-Auth"
    },
    var.tags
  )
}
