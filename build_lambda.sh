#!/bin/bash

# Build script for JWT Lambda Authorizer deployment package

set -e

echo "Building JWT Lambda Authorizer deployment package..."

# Create temporary directory
TEMP_DIR=$(mktemp -d)
echo "Using temporary directory: $TEMP_DIR"

# Copy Lambda function code
cp lambda_function.py "$TEMP_DIR/"

# Copy requirements.txt
cp requirements.txt "$TEMP_DIR/"

# Change to temp directory
cd "$TEMP_DIR"

# Install dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt -t .

# Remove unnecessary files to reduce package size
echo "Cleaning up unnecessary files..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name "*.dist-info" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name "tests" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "*.pyo" -delete 2>/dev/null || true

# Create the zip file
echo "Creating deployment package..."
zip -r lambda-authorizer.zip . -x "*.git*" "*.DS_Store*" "*__pycache__*"

# Move the zip file back to the original directory
mv lambda-authorizer.zip "$OLDPWD/"

# Clean up
cd "$OLDPWD"
rm -rf "$TEMP_DIR"

echo "✅ Lambda deployment package created: lambda-authorizer.zip"
echo "📦 Package size: $(du -h lambda-authorizer.zip | cut -f1)"

# Verify the package contents
echo "📋 Package contents:"
unzip -l lambda-authorizer.zip | head -20
