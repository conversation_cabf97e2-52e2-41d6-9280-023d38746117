# Example Terraform variables file for JWT Token Manager
# Copy this file to terraform.tfvars and update the values

# AWS Configuration
region      = "us-east-1"
environment = "dev"

# JWT Configuration
# IMPORTANT: Generate a secure random key for production
# You can use: openssl rand -base64 32
jwt_secret_key = "mVptYqUuxgzUpzbBeEBGEMeGQ4yknQDwJwDeKRykvrS8_LLOlKcLGp4YgT_yoF8r-LPoN-eUcsgrtDoiKPyt8Q"

# Token TTL Configuration (in seconds)
access_token_ttl  = "900"    # 15 minutes
refresh_token_ttl = "604800" # 7 days
token_cache_ttl   = "300"    # 5 minutes

# Lambda Configuration
lambda_memory_size = 512
lambda_timeout     = 30

# Logging Configuration
log_retention_days = 14

# API Gateway Configuration
enable_api_gateway_logging        = true
api_gateway_throttle_rate_limit   = 1000
api_gateway_throttle_burst_limit  = 2000

# Additional Tags
tags = {
  Owner       = "Team Penguin"
  CostCenter  = "Engineering"
  Application = "JWT-Authentication"
}
