# JWT Lambda Authorizer - Deployment Checklist

## ✅ Pre-Deployment Verification

### 📁 Required Files
- [x] `main.tf` - Terraform configuration for JWT infrastructure
- [x] `variables.tf` - Variable definitions
- [x] `terraform.tfvars.example` - Example configuration
- [x] `lambda_function.py` - JWT Lambda authorizer code
- [x] `requirements.txt` - Python dependencies
- [x] `lambda-authorizer.zip` - Deployment package (138 files)
- [x] `README.md` - Complete documentation

### 🔧 Infrastructure Components
- [x] **DynamoDB Table**: JWT tokens and sessions storage with TTL
- [x] **Lambda Function**: JWT token manager and authorizer
- [x] **API Gateway**: Authentication endpoints and protected resources
- [x] **IAM Roles**: Proper permissions for Lambda and DynamoDB
- [x] **CloudWatch**: Logging and monitoring setup

### 🔐 Security Configuration
- [x] **JWT Secret Key**: Environment variable configuration
- [x] **Session Management**: DynamoDB-based session tracking
- [x] **Token Expiration**: Configurable TTL for access and refresh tokens
- [x] **Audit Logging**: Comprehensive logging with correlation IDs
- [x] **Error Handling**: Specific error types and security events

## 🚀 Deployment Steps

### 1. Configure Variables
```bash
# Copy example configuration
cp terraform.tfvars.example terraform.tfvars

# Edit terraform.tfvars with your values
# IMPORTANT: Set a secure JWT secret key (at least 32 characters)
```

### 2. Initialize Terraform
```bash
terraform init
```

### 3. Plan Deployment
```bash
terraform plan
```

### 4. Deploy Infrastructure
```bash
terraform apply
```

### 5. Verify Deployment
```bash
# Check outputs
terraform output

# Test API endpoints
curl -X POST https://your-api-gateway-url/dev/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 📋 Required terraform.tfvars Configuration

```hcl
# AWS Configuration
region      = "us-east-1"
environment = "dev"

# JWT Configuration - CHANGE THIS!
jwt_secret_key = "your-super-secure-jwt-secret-key-at-least-32-characters-long"

# Token TTL Configuration (in seconds)
access_token_ttl  = "900"    # 15 minutes
refresh_token_ttl = "604800" # 7 days
token_cache_ttl   = "300"    # 5 minutes

# Optional: Customize other settings
lambda_memory_size = 512
lambda_timeout     = 30
log_retention_days = 14
```

## 🔍 Post-Deployment Verification

### API Endpoints
- `POST /auth/login` - User authentication
- `POST /auth/refresh` - Token refresh
- `POST /auth/logout` - Session logout
- `GET /protected` - Protected test endpoint

### Test Authentication Flow
```bash
# 1. Login and get tokens
LOGIN_RESPONSE=$(curl -s -X POST https://your-api-url/dev/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}')

# 2. Extract access token
ACCESS_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.access_token')

# 3. Test protected endpoint
curl -H "Authorization: Bearer $ACCESS_TOKEN" \
  https://your-api-url/dev/protected

# 4. Test token refresh
REFRESH_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.refresh_token')
curl -X POST https://your-api-url/dev/auth/refresh \
  -H "Content-Type: application/json" \
  -d "{\"refresh_token\":\"$REFRESH_TOKEN\"}"
```

### CloudWatch Monitoring
- Check Lambda function logs: `/aws/lambda/YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-{env}`
- Monitor authentication metrics and error rates
- Set up alerts for authentication failures

### DynamoDB Verification
```bash
# Check JWT tokens table
aws dynamodb scan --table-name YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev --max-items 5
```

## 🛠️ Troubleshooting

### Common Issues

1. **JWT Secret Key Too Short**
   ```
   Error: JWT secret key must be at least 32 characters long
   Solution: Update jwt_secret_key in terraform.tfvars
   ```

2. **Lambda Package Too Large**
   ```
   Error: Deployment package size exceeds limit
   Solution: Remove test dependencies from requirements.txt for production
   ```

3. **DynamoDB Permission Errors**
   ```
   Error: AccessDeniedException
   Solution: Verify IAM role has DynamoDB permissions
   ```

4. **API Gateway CORS Issues**
   ```
   Error: CORS policy blocks request
   Solution: Check API Gateway CORS configuration
   ```

### Debug Commands
```bash
# Check Terraform state
terraform show

# Validate configuration
terraform validate

# Check Lambda function
aws lambda get-function --function-name YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev

# View CloudWatch logs
aws logs tail /aws/lambda/YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev --follow
```

## 📊 Monitoring Setup

### CloudWatch Dashboards
- Authentication success/failure rates
- Token refresh frequency
- API Gateway request metrics
- Lambda function performance

### Recommended Alerts
- Authentication failure rate > 10%
- Lambda function errors > 5%
- DynamoDB throttling events
- API Gateway 5xx errors

## 🔄 Maintenance

### Regular Tasks
- Monitor JWT secret key rotation (recommended: every 90 days)
- Review and clean up expired sessions
- Update Lambda function dependencies
- Monitor CloudWatch costs and log retention

### Scaling Considerations
- DynamoDB auto-scaling for high traffic
- Lambda concurrency limits
- API Gateway throttling settings
- CloudWatch log retention policies

## 📚 Additional Resources

- [README.md](README.md) - Complete feature documentation
- [SECURITY_GUIDE.md](SECURITY_GUIDE.md) - Security best practices
- [test_lambda_authorizer.py](test_lambda_authorizer.py) - Unit tests
- [AWS Lambda Documentation](https://docs.aws.amazon.com/lambda/)
- [AWS API Gateway Documentation](https://docs.aws.amazon.com/apigateway/)

## ✅ Deployment Ready

All components are configured and ready for deployment:
- ✅ Infrastructure code (Terraform)
- ✅ Application code (Lambda function)
- ✅ Deployment package (lambda-authorizer.zip)
- ✅ Configuration templates
- ✅ Documentation and guides
- ✅ Testing framework

**Next Step**: Configure `terraform.tfvars` and run `terraform apply`
