

provider "aws" {
  region = var.region
}

# Data sources
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# IAM Role for Lambda Authorizer
resource "aws_iam_role" "lambda_authorizer" {
  name = "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# IAM Policy for JWT Lambda Authorizer
resource "aws_iam_policy" "lambda_authorizer" {
  name        = "YANTECH-YNP01-AWS-Lambda-JWTAuthorizerPolicy-${var.environment}"
  description = "Permissions for JWT Lambda Authorizer"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = [
          aws_dynamodb_table.jwt_tokens.arn,
          "${aws_dynamodb_table.jwt_tokens.arn}/index/*"
        ]
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "lambda_authorizer" {
  role       = aws_iam_role.lambda_authorizer.name
  policy_arn = aws_iam_policy.lambda_authorizer.arn
}

# DynamoDB Table for JWT Tokens and Sessions
resource "aws_dynamodb_table" "jwt_tokens" {
  name         = "YANTECH-YNP01-AWS-DynamoDB-JWTTokens-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "session_id"

  attribute {
    name = "session_id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  # Global Secondary Index for user-based queries
  global_secondary_index {
    name     = "user_id-index"
    hash_key = "user_id"
    projection_type = "ALL"

  }

  # TTL configuration for automatic cleanup
  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  tags = {
    Environment = "${var.environment}"
    Project     = "YNP01"
    Purpose     = "JWT-Session-Management"
  }
}

# Lambda Function for JWT Token Management
resource "aws_lambda_function" "jwt_authorizer" {
  filename         = "lambda-authorizer.zip"
  function_name    = "YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-${var.environment}"
  role             = aws_iam_role.lambda_authorizer.arn
  handler          = "lambda_function.lambda_handler"
  runtime          = "python3.9"
  timeout          = 30
  memory_size      = 512
  source_code_hash = filebase64sha256("lambda-authorizer.zip")

  environment {
    variables = {
      JWT_TOKENS_TABLE  = aws_dynamodb_table.jwt_tokens.name
      JWT_SECRET_KEY    = var.jwt_secret_key
      ACCESS_TOKEN_TTL  = var.access_token_ttl
      REFRESH_TOKEN_TTL = var.refresh_token_ttl
      TOKEN_CACHE_TTL   = var.token_cache_ttl
      ENVIRONMENT       = var.environment
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.lambda_authorizer,
    aws_cloudwatch_log_group.lambda_logs,
  ]
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "lambda_logs" {
  name              = "/aws/lambda/YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-${var.environment}"
  retention_in_days = 14

  tags = {
    Environment = var.environment
    Project     = "YNP01"
  }
}

# API Gateway for JWT Authentication
resource "aws_api_gateway_rest_api" "main" {
  name        = "YANTECH-YNP01-AWS-JWT-APIGateway-${var.environment}"
  description = "JWT Token Management API Gateway"

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = {
    Environment = var.environment
    Project     = "YNP01"
  }
}

# JWT Token Authorizer for API Gateway
resource "aws_api_gateway_authorizer" "jwt_authorizer" {
  name                   = "YANTECH-YNP01-AWS-JWT-Authorizer-${var.environment}"
  rest_api_id            = aws_api_gateway_rest_api.main.id
  authorizer_uri         = aws_lambda_function.jwt_authorizer.invoke_arn
  identity_source        = "method.request.header.Authorization"
  type                   = "TOKEN"
  authorizer_credentials = aws_iam_role.lambda_authorizer.arn
}

# Lambda Permission for API Gateway Authorizer
resource "aws_lambda_permission" "apigw_authorizer" {
  statement_id  = "AllowAPIGatewayInvokeAuthorizer"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.jwt_authorizer.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/authorizers/${aws_api_gateway_authorizer.jwt_authorizer.id}"
}

# Lambda Permission for API Gateway Methods
resource "aws_lambda_permission" "apigw_methods" {
  statement_id  = "AllowAPIGatewayInvokeMethods"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.jwt_authorizer.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/*/*"
}

# JWT Authentication API Resources

# /auth resource
resource "aws_api_gateway_resource" "auth" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "auth"
}

# /auth/login resource
resource "aws_api_gateway_resource" "login" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "login"
}

# /auth/refresh resource
resource "aws_api_gateway_resource" "refresh" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "refresh"
}

# /auth/logout resource
resource "aws_api_gateway_resource" "logout" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_resource.auth.id
  path_part   = "logout"
}

# Login Method (POST /auth/login)
resource "aws_api_gateway_method" "login" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.login.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "login" {
  rest_api_id             = aws_api_gateway_rest_api.main.id
  resource_id             = aws_api_gateway_resource.login.id
  http_method             = aws_api_gateway_method.login.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.jwt_authorizer.invoke_arn
}

# Refresh Method (POST /auth/refresh)
resource "aws_api_gateway_method" "refresh" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.refresh.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "refresh" {
  rest_api_id             = aws_api_gateway_rest_api.main.id
  resource_id             = aws_api_gateway_resource.refresh.id
  http_method             = aws_api_gateway_method.refresh.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.jwt_authorizer.invoke_arn
}

# Logout Method (POST /auth/logout)
resource "aws_api_gateway_method" "logout" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.logout.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.jwt_authorizer.id
}

resource "aws_api_gateway_integration" "logout" {
  rest_api_id             = aws_api_gateway_rest_api.main.id
  resource_id             = aws_api_gateway_resource.logout.id
  http_method             = aws_api_gateway_method.logout.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.jwt_authorizer.invoke_arn
}

# Protected Test Resource
resource "aws_api_gateway_resource" "protected" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "protected"
}

resource "aws_api_gateway_method" "protected" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.protected.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.jwt_authorizer.id
}

resource "aws_api_gateway_integration" "protected" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_resource.protected.id
  http_method = aws_api_gateway_method.protected.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

# Deploy JWT API
resource "aws_api_gateway_deployment" "jwt_api" {
  depends_on = [
    aws_api_gateway_integration.login,
    aws_api_gateway_integration.refresh,
    aws_api_gateway_integration.logout,
    aws_api_gateway_integration.protected
  ]
  rest_api_id = aws_api_gateway_rest_api.main.id

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.auth.id,
      aws_api_gateway_resource.login.id,
      aws_api_gateway_resource.refresh.id,
      aws_api_gateway_resource.logout.id,
      aws_api_gateway_resource.protected.id,
      aws_api_gateway_method.login.id,
      aws_api_gateway_method.refresh.id,
      aws_api_gateway_method.logout.id,
      aws_api_gateway_method.protected.id,
      aws_api_gateway_integration.login.id,
      aws_api_gateway_integration.refresh.id,
      aws_api_gateway_integration.logout.id,
      aws_api_gateway_integration.protected.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_api_gateway_stage" "jwt_api" {
  stage_name    = var.environment
  rest_api_id   = aws_api_gateway_rest_api.main.id
  deployment_id = aws_api_gateway_deployment.jwt_api.id

  tags = {
    Environment = var.environment
    Project     = "YNP01"
  }
}

# Outputs
output "api_gateway_url" {
  value       = "https://${aws_api_gateway_rest_api.main.id}.execute-api.${data.aws_region.current.name}.amazonaws.com/${aws_api_gateway_stage.jwt_api.stage_name}"
  description = "JWT API Gateway URL"
}

output "jwt_tokens_table_name" {
  value       = aws_dynamodb_table.jwt_tokens.name
  description = "DynamoDB table name for JWT tokens"
}

output "lambda_function_name" {
  value       = aws_lambda_function.jwt_authorizer.function_name
  description = "JWT Lambda authorizer function name"
}