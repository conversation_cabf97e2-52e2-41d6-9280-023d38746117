{"version": 4, "terraform_version": "1.5.3", "serial": 63, "lineage": "0169c89d-1d3d-ef83-7ae4-1ba24df90b56", "outputs": {"api_gateway_url": {"value": "https://nygb6pglsd.execute-api.us-east-1.amazonaws.com/dev", "type": "string"}, "jwt_tokens_table_name": {"value": "YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev", "type": "string"}, "lambda_function_name": {"value": "YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/bichesq-NP", "id": "************", "user_id": "AIDA6GGOYOFHCJSNZXTM6"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US East (N. Virginia)", "endpoint": "ec2.us-east-1.amazonaws.com", "id": "us-east-1", "name": "us-east-1", "region": "us-east-1"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_api_gateway_authorizer", "name": "jwt_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:apigateway:us-east-1::/restapis/nygb6pglsd/authorizers/2midxf", "authorizer_credentials": "arn:aws:iam::************:role/YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "authorizer_result_ttl_in_seconds": 300, "authorizer_uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev/invocations", "id": "2midxf", "identity_source": "method.request.header.Authorization", "identity_validation_expression": "", "name": "YANTECH-YNP01-AWS-JWT-Authorizer-dev", "provider_arns": null, "region": "us-east-1", "rest_api_id": "nygb6pglsd", "type": "TOKEN"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_deployment", "name": "jwt_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"created_date": "2025-09-01T12:33:01Z", "description": "", "id": "ae3p6s", "region": "us-east-1", "rest_api_id": "nygb6pglsd", "triggers": {"redeployment": "ec3d2554c7cba10db11a4e736068db01cfb5a346"}, "variables": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.jwt_authorizer", "aws_api_gateway_integration.login", "aws_api_gateway_integration.logout", "aws_api_gateway_integration.protected", "aws_api_gateway_integration.refresh", "aws_api_gateway_method.login", "aws_api_gateway_method.logout", "aws_api_gateway_method.protected", "aws_api_gateway_method.refresh", "aws_api_gateway_resource.auth", "aws_api_gateway_resource.login", "aws_api_gateway_resource.logout", "aws_api_gateway_resource.protected", "aws_api_gateway_resource.refresh", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "login", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "nq956e", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-nygb6pglsd-nq956e-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": null, "request_templates": null, "resource_id": "nq956e", "rest_api_id": "nygb6pglsd", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.login", "aws_api_gateway_resource.auth", "aws_api_gateway_resource.login", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "logout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "donyt6", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-nygb6pglsd-donyt6-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": null, "request_templates": null, "resource_id": "donyt6", "rest_api_id": "nygb6pglsd", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.jwt_authorizer", "aws_api_gateway_method.logout", "aws_api_gateway_resource.auth", "aws_api_gateway_resource.logout", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "protected", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "98jc0b", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-nygb6pglsd-98jc0b-GET", "integration_http_method": "", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": null, "request_templates": {"application/json": "{\"statusCode\":200}"}, "resource_id": "98jc0b", "rest_api_id": "nygb6pglsd", "timeout_milliseconds": 29000, "tls_config": [], "type": "MOCK", "uri": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.jwt_authorizer", "aws_api_gateway_method.protected", "aws_api_gateway_resource.protected", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "refresh", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "mqzfmq", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-nygb6pglsd-mqzfmq-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": null, "request_templates": null, "resource_id": "mqzfmq", "rest_api_id": "nygb6pglsd", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.refresh", "aws_api_gateway_resource.auth", "aws_api_gateway_resource.refresh", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "login", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": null, "authorizer_id": null, "http_method": "POST", "id": "agm-nygb6pglsd-nq956e-POST", "operation_name": null, "region": "us-east-1", "request_models": null, "request_parameters": null, "request_validator_id": null, "resource_id": "nq956e", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.auth", "aws_api_gateway_resource.login", "aws_api_gateway_rest_api.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "logout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": null, "authorizer_id": "2midxf", "http_method": "POST", "id": "agm-nygb6pglsd-donyt6-POST", "operation_name": null, "region": "us-east-1", "request_models": null, "request_parameters": null, "request_validator_id": null, "resource_id": "donyt6", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.jwt_authorizer", "aws_api_gateway_resource.auth", "aws_api_gateway_resource.logout", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "protected", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": null, "authorizer_id": "2midxf", "http_method": "GET", "id": "agm-nygb6pglsd-98jc0b-GET", "operation_name": null, "region": "us-east-1", "request_models": null, "request_parameters": null, "request_validator_id": null, "resource_id": "98jc0b", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.jwt_authorizer", "aws_api_gateway_resource.protected", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "refresh", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": null, "authorizer_id": null, "http_method": "POST", "id": "agm-nygb6pglsd-mqzfmq-POST", "operation_name": null, "region": "us-east-1", "request_models": null, "request_parameters": null, "request_validator_id": null, "resource_id": "mqzfmq", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.auth", "aws_api_gateway_resource.refresh", "aws_api_gateway_rest_api.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "auth", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "omr52s", "parent_id": "9esfdcwukf", "path": "/auth", "path_part": "auth", "region": "us-east-1", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "login", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "nq956e", "parent_id": "omr52s", "path": "/auth/login", "path_part": "login", "region": "us-east-1", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.auth", "aws_api_gateway_rest_api.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "logout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "donyt6", "parent_id": "omr52s", "path": "/auth/logout", "path_part": "logout", "region": "us-east-1", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.auth", "aws_api_gateway_rest_api.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "protected", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "98jc0b", "parent_id": "9esfdcwukf", "path": "/protected", "path_part": "protected", "region": "us-east-1", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "refresh", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "mqzfmq", "parent_id": "omr52s", "path": "/auth/refresh", "path_part": "refresh", "region": "us-east-1", "rest_api_id": "nygb6pglsd"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.auth", "aws_api_gateway_rest_api.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_rest_api", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_source": "HEADER", "arn": "arn:aws:apigateway:us-east-1::/restapis/nygb6pglsd", "binary_media_types": [], "body": null, "created_date": "2025-09-01T12:32:09Z", "description": "JWT Token Management API Gateway", "disable_execute_api_endpoint": false, "endpoint_configuration": [{"ip_address_type": "ipv4", "types": ["REGIONAL"], "vpc_endpoint_ids": []}], "execution_arn": "arn:aws:execute-api:us-east-1:************:nygb6pglsd", "fail_on_warnings": null, "id": "nygb6pglsd", "minimum_compression_size": "", "name": "YANTECH-YNP01-AWS-JWT-APIGateway-dev", "parameters": null, "policy": "", "put_rest_api_mode": null, "region": "us-east-1", "root_resource_id": "9esfdcwukf", "tags": {"Environment": "dev", "Project": "YNP01"}, "tags_all": {"Environment": "dev", "Project": "YNP01"}}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_stage", "name": "jwt_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_log_settings": [], "arn": "arn:aws:apigateway:us-east-1::/restapis/nygb6pglsd/stages/dev", "cache_cluster_enabled": false, "cache_cluster_size": "", "canary_settings": [], "client_certificate_id": "", "deployment_id": "ae3p6s", "description": "", "documentation_version": "", "execution_arn": "arn:aws:execute-api:us-east-1:************:nygb6pglsd/dev", "id": "ags-nygb6pglsd-dev", "invoke_url": "https://nygb6pglsd.execute-api.us-east-1.amazonaws.com/dev", "region": "us-east-1", "rest_api_id": "nygb6pglsd", "stage_name": "dev", "tags": {"Environment": "dev", "Project": "YNP01"}, "tags_all": {"Environment": "dev", "Project": "YNP01"}, "variables": null, "web_acl_arn": "", "xray_tracing_enabled": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.jwt_authorizer", "aws_api_gateway_deployment.jwt_api", "aws_api_gateway_integration.login", "aws_api_gateway_integration.logout", "aws_api_gateway_integration.protected", "aws_api_gateway_integration.refresh", "aws_api_gateway_method.login", "aws_api_gateway_method.logout", "aws_api_gateway_method.protected", "aws_api_gateway_method.refresh", "aws_api_gateway_resource.auth", "aws_api_gateway_resource.login", "aws_api_gateway_resource.logout", "aws_api_gateway_resource.protected", "aws_api_gateway_resource.refresh", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"]}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "lambda_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/aws/lambda/YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "id": "/aws/lambda/YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/lambda/YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "name_prefix": "", "region": "us-east-1", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "dev", "Project": "YNP01"}, "tags_all": {"Environment": "dev", "Project": "YNP01"}}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_dynamodb_table", "name": "jwt_tokens", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:us-east-1:************:table/YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev", "attribute": [{"name": "session_id", "type": "S"}, {"name": "user_id", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [{"hash_key": "user_id", "name": "user_id-index", "non_key_attributes": [], "on_demand_throughput": [], "projection_type": "ALL", "range_key": "", "read_capacity": 0, "write_capacity": 0}], "hash_key": "session_id", "id": "YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev", "import_table": [], "local_secondary_index": [], "name": "YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": false, "recovery_period_in_days": 0}], "range_key": null, "read_capacity": 0, "region": "us-east-1", "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"Environment": "dev", "Project": "YNP01", "Purpose": "JWT-Session-Management"}, "tags_all": {"Environment": "dev", "Project": "YNP01", "Purpose": "JWT-Session-Management"}, "timeouts": null, "ttl": [{"attribute_name": "ttl", "enabled": true}], "write_capacity": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-JWTAuthorizerPolicy-dev", "attachment_count": 0, "description": "Permissions for JWT Lambda Authorizer", "id": "arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-JWTAuthorizerPolicy-dev", "name": "YANTECH-YNP01-AWS-Lambda-JWTAuthorizerPolicy-dev", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"dynamodb:GetItem\",\"dynamodb:PutItem\",\"dynamodb:UpdateItem\",\"dynamodb:Query\",\"dynamodb:Scan\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:us-east-1:************:table/YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev\",\"arn:aws:dynamodb:us-east-1:************:table/YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev/index/*\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA6GGOYOFHDH2Q2RJV5", "tags": null, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_dynamodb_table.jwt_tokens"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-09-01T12:32:09Z", "description": "", "force_detach_policies": false, "id": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": null, "tags_all": {}, "unique_id": "AROA6GGOYOFHE6GBAB2IN"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev/arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-JWTAuthorizerPolicy-dev", "policy_arn": "arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-JWTAuthorizerPolicy-dev", "role": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_lambda_function", "name": "jwt_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "code_sha256": "pmO7SYJlRx8Y3VLJjqUiB1uO9sJYzfnJpefEgpRYdU4=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"ACCESS_TOKEN_TTL": "900", "ENVIRONMENT": "dev", "JWT_SECRET_KEY": "mVptYqUuxgzUpzbBeEBGEMeGQ4yknQDwJwDeKRykvrS8_LLOlKcLGp4YgT_yoF8r-LPoN-eUcsgrtDoiKPyt8Q", "JWT_TOKENS_TABLE": "YANTECH-YNP01-AWS-DynamoDB-JWTTokens-dev", "REFRESH_TOKEN_TTL": "604800", "TOKEN_CACHE_TTL": "300"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "lambda-authorizer.zip", "function_name": "YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "handler": "lambda_function.lambda_handler", "id": "YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev/invocations", "kms_key_arn": "", "last_modified": "2025-09-01T12:32:51.625+0000", "layers": null, "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "system_log_level": ""}], "memory_size": 512, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev:$LATEST/invocations", "region": "us-east-1", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "runtime": "python3.9", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "pmO7SYJlRx8Y3VLJjqUiB1uO9sJYzfnJpefEgpRYdU4=", "source_code_size": 340982, "tags": null, "tags_all": {}, "timeout": 30, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "environment"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "variables"}, {"type": "index", "value": {"value": "JWT_SECRET_KEY", "type": "string"}}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_lambda_permission", "name": "apigw_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "function_url_auth_type": null, "id": "AllowAPIGatewayInvokeAuthorizer", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "region": "us-east-1", "source_account": null, "source_arn": "arn:aws:execute-api:us-east-1:************:nygb6pglsd/authorizers/2midxf", "statement_id": "AllowAPIGatewayInvokeAuthorizer", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.jwt_authorizer", "aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"]}]}, {"mode": "managed", "type": "aws_lambda_permission", "name": "apigw_methods", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "YANTECH-YNP01-AWS-Lambda-JWTAuthorizer-dev", "function_url_auth_type": null, "id": "AllowAPIGatewayInvokeMethods", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "region": "us-east-1", "source_account": null, "source_arn": "arn:aws:execute-api:us-east-1:************:nygb6pglsd/*/*", "statement_id": "AllowAPIGatewayInvokeMethods", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.main", "aws_cloudwatch_log_group.lambda_logs", "aws_dynamodb_table.jwt_tokens", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_iam_role_policy_attachment.lambda_authorizer", "aws_lambda_function.jwt_authorizer"]}]}], "check_results": null}